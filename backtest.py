'''
IC backtest framework based on level2_utils.load_l2ticks

Usage:
    1. Implement your own Factor class inheriting from FactorBase and override `compute(self, snapshot_df)`
       to calculate factor value given a tick snapshot DataFrame (one row).
    2. In `if __name__ == "__main__":` part, fill in `codes`, `start_date`, `end_date` and replace
       `<PERSON>pleF<PERSON>` with your factor class.
    3. Run the script. It will:
         • Pull 09:40 snapshot for each security every trading day.
         • Compute factor values, the forward 1-day 09:40 return,
           the daily Spearman IC and its cumulative sum.
         • Show a cumulative-IC chart.
    4. The function `run_backtest` can also be imported and reused.

Note:
    - This version has **no dependency on jqdatasdk**. It builds a simple trading calendar
      via pandas' `bdate_range`, which treats all weekdays (Mon‑Fri) as open days.
      If you need to filter official exchange holidays, replace `get_trade_days`
      with your own calendar provider.
'''

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

from level2_utils import load_l2ticks  # data loader from level2_utils.py

# ---------------------------------------------------------------------
# Minimal trading‑day calendar (Mon‑Fri only, no holiday adjustment)
# ---------------------------------------------------------------------

def get_trade_days(start_date, end_date=None, count=None):
    """
    Return a list of trading days (datetime.date).

    Args:
        start_date (str | datetime): inclusive start day.
        end_date   (str | datetime, optional): inclusive end day.
        count      (int, optional):  number of trading days to fetch
                                     starting from `start_date`.

    Exactly one of `end_date` or `count` should be given.  Official
    exchange holidays are NOT excluded in this simple version.
    """
    start = pd.to_datetime(start_date).normalize()

    if count is not None:
        dates = pd.bdate_range(start=start, periods=count)
    else:
        if end_date is None:
            raise ValueError('Either end_date or count must be provided.')
        end = pd.to_datetime(end_date).normalize()
        dates = pd.bdate_range(start=start, end=end)

    return [d.date() for d in dates]

# ---------------------------------------------------------------------
# Constants
# ---------------------------------------------------------------------
PRICE_FIELD = 'current'
TIME_FIELD  = 'time'
SNAP_TIME   = '09:40:00'

# ---------------------------------------------------------------------
# Factor interface
# ---------------------------------------------------------------------
class FactorBase:
    '''Standardised factor interface: return a scalar for one security snapshot.'''
    name: str = 'UnnamedFactor'

    def compute(self, snapshot_df: pd.DataFrame) -> float:
        raise NotImplementedError

    def __call__(self, snapshot_df: pd.DataFrame) -> float:
        return self.compute(snapshot_df)

# ---------------------------------------------------------------------
# Example factor
# ---------------------------------------------------------------------
class SampleFactor(FactorBase):
    '''Toy factor: last price itself.'''
    name = 'LastPrice'

    def compute(self, snapshot_df: pd.DataFrame) -> float:
        return float(snapshot_df[PRICE_FIELD].iloc[0])

# ---------------------------------------------------------------------
# Timestamp helper
# ---------------------------------------------------------------------
try:
    from ft_common_base import dt2ts
except ImportError:  # fallback
    dt2ts = lambda dt: int(pd.Timestamp(dt).timestamp())

# ---------------------------------------------------------------------
# Data helpers
# ---------------------------------------------------------------------

def _snapshot(code: str, date, snap_time: str = SNAP_TIME, fields=None):
    '''Fetch tick snapshot nearest to `date snap_time`.'''
    df = load_l2ticks(code=code, date=date, fields=fields or [TIME_FIELD, PRICE_FIELD])
    if df.empty:
        return None
    target_ts = dt2ts(f'{date} {snap_time}')
    # exact hit window of 60s
    mask = (df[TIME_FIELD] >= target_ts) & (df[TIME_FIELD] < target_ts + 60)
    if mask.any():
        return df.loc[mask].head(1)
    # otherwise choose closest
    idx = (df[TIME_FIELD] - target_ts).abs().argsort().iloc[0]
    return df.iloc[[idx]]

def _daily_factor(codes, date, factor: FactorBase, snap_time=SNAP_TIME):
    vals = {}
    for code in codes:
        snap = _snapshot(code, date, snap_time)
        if snap is not None and not snap.empty:
            try:
                vals[code] = factor(snap)
            except Exception:
                continue
    return pd.Series(vals, name=pd.Timestamp(date))

def _forward_return(codes, date, snap_time=SNAP_TIME):
    '''(P_{t+1}/P_t − 1) based on 09:40 snapshots.'''
    trade_dates = get_trade_days(start_date=date, count=2)
    if len(trade_dates) < 2:
        return pd.Series(dtype=float)
    next_date = trade_dates[1]
    today_p, next_p = {}, {}
    for code in codes:
        s0 = _snapshot(code, date, snap_time)
        s1 = _snapshot(code, next_date, snap_time)
        if (s0 is not None and not s0.empty and s1 is not None and not s1.empty):
            today_p[code] = s0[PRICE_FIELD].iloc[0]
            next_p[code] = s1[PRICE_FIELD].iloc[0]
    common = set(today_p).intersection(next_p)
    ret = {c: (next_p[c] - today_p[c]) / today_p[c] for c in common}
    return pd.Series(ret, name=pd.Timestamp(next_date))

# ---------------------------------------------------------------------
# Back‑test driver
# ---------------------------------------------------------------------

def ic_series(codes, factor: FactorBase, start_date, end_date=None, snap_time=SNAP_TIME):
    if end_date is None:
        end_date = (pd.to_datetime(start_date) + pd.DateOffset(years=1)).date()
    dates = get_trade_days(start_date=start_date, end_date=end_date)
    ics = []
    for d in dates[:-1]:  # last date lacks next-day return
        f = _daily_factor(codes, d, factor, snap_time)
        r = _forward_return(codes, d, snap_time)
        common = f.index.intersection(r.index)
        if len(common) >= 3:
            ic_val = f[common].corr(r[common], method='spearman')
        else:
            ic_val = np.nan
        ics.append({'date': d, 'ic': ic_val})
    return pd.DataFrame(ics).set_index('date').sort_index()

def plot_ic_cumsum(ic_df, title='Cumulative IC'):
    ic_df['cum_ic'] = ic_df['ic'].fillna(0).cumsum()
    ic_df['cum_ic'].plot(title=title, figsize=(10, 4))
    plt.ylabel('Cumulative IC')
    plt.tight_layout()
    plt.show()

def run_backtest(codes, factor_cls, start_date, end_date=None, snap_time=SNAP_TIME, plot=True):
    factor = factor_cls()
    ic_df = ic_series(codes, factor, start_date, end_date, snap_time)
    if plot:
        plot_ic_cumsum(ic_df, title=f'{factor.name} – Cumulative IC')
    return ic_df

# ---------------------------------------------------------------------
# Example usage
# ---------------------------------------------------------------------
if __name__ == '__main__':
    # ---- user configurable area ------------------------------------
    CODES = ['600000.XSHG', '000001.XSHE']   # securities universe
    START_DATE = '2024-01-02'                # YYYY-MM-DD
    END_DATE = None                          # None → one year later
    FACTOR_CLASS = SampleFactor             # replace with your factor
    # ---------------------------------------------------------------

    results = run_backtest(
        CODES,
        FACTOR_CLASS,
        START_DATE,
        END_DATE,
    )
    # `results` is a DataFrame with daily IC and cumulative IC columns
